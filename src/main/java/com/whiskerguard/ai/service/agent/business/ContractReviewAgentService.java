/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ContractReviewAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：合同审查智能体服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.service.agent.core.KnowledgeRetrievalService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.LlmRequest;
import com.whiskerguard.ai.service.contract.ContractReviewService;
import com.whiskerguard.ai.service.dto.ContractReviewRequestDTO;
import com.whiskerguard.ai.service.dto.ContractReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.util.LlmResponseProcessor;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 合同审查智能体服务
 * <p>
 * 负责对合同进行智能审查，包括关联方分析、条款审查、风险评估等。
 * 通过AI分析合同内容，检查合规性并识别潜在风险。
 *
 * 主要功能：
 * 1. 合同关联方审查
 * 2. 合同条款分析
 * 3. 法律合规性检查
 * 4. 内部制度符合性检查
 * 5. 风险点识别和评估
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class ContractReviewAgentService {

    private static final Logger log = LoggerFactory.getLogger(ContractReviewAgentService.class);

    private final KnowledgeRetrievalService knowledgeRetrievalService;
    private final LlmOrchestrationService llmOrchestrationService;
    private final AiInvocationService aiInvocationService;
    private final RetrievalServiceClient retrievalServiceClient;
    private final ContractReviewService contractReviewService;

    /**
     * 默认重试次数
     */
    private static final int DEFAULT_MAX_RETRIES = 3;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public ContractReviewAgentService(
        KnowledgeRetrievalService knowledgeRetrievalService,
        LlmOrchestrationService llmOrchestrationService,
        AiInvocationService aiInvocationService,
        RetrievalServiceClient retrievalServiceClient,
        ContractReviewService contractReviewService
    ) {
        this.knowledgeRetrievalService = knowledgeRetrievalService;
        this.llmOrchestrationService = llmOrchestrationService;
        this.aiInvocationService = aiInvocationService;
        this.retrievalServiceClient = retrievalServiceClient;
        this.contractReviewService = contractReviewService;
    }

    /**
     * 审查合同
     * <p>
     * 使用Agent方式处理合同审查，自动调度多个步骤的执行，并整合结果
     *
     * @param request 合同审查请求
     * @return 合同审查响应
     */
    public ContractReviewResponseDTO reviewContract(ContractReviewRequestDTO request) {
        log.info("开始审查合同，合同ID: {}, 合同类型: {}", request.getContractId(), request.getContractType());

        try {
            // 尝试优先使用现有的ContractReviewService服务
            // 使用重试机制确保更高的可靠性
            return executeWithRetry(() -> contractReviewService.reviewContract(request), DEFAULT_MAX_RETRIES);
        } catch (Exception primaryServiceException) {
            log.warn("使用主合同审查服务失败，转为使用Agent分步处理：{}", primaryServiceException.getMessage());

            // 如果主服务调用失败，使用Agent方式分步处理
            return processContractReviewByAgent(request);
        }
    }

    /**
     * 使用Agent方式分步处理合同审查
     */
    private ContractReviewResponseDTO processContractReviewByAgent(ContractReviewRequestDTO request) {
        try {
            log.info("使用Agent方式分步处理合同审查，合同ID: {}", request.getContractId());

            // 并行执行知识检索任务
            CompletableFuture<String> partyInfoFuture = CompletableFuture.supplyAsync(() -> retrievePartyInformation(request));
            CompletableFuture<String> legalRegulationsFuture = CompletableFuture.supplyAsync(() -> retrieveLegalRegulations(request));
            CompletableFuture<String> internalPoliciesFuture = CompletableFuture.supplyAsync(() -> retrieveInternalPolicies(request));

            // 等待所有检索任务完成
            CompletableFuture.allOf(partyInfoFuture, legalRegulationsFuture, internalPoliciesFuture).join();

            // 获取检索结果
            String partyInfo = partyInfoFuture.get();
            String legalRegulations = legalRegulationsFuture.get();
            String internalPolicies = internalPoliciesFuture.get();

            log.debug("知识检索完成，开始并行执行分析任务");

            // 准备并行处理任务
            List<LlmRequest> parallelTasks = prepareParallelAnalysisTasks(request, partyInfo, legalRegulations, internalPolicies);

            // 并行执行分析任务
            List<String> analysisResults = llmOrchestrationService.batchInvokeLlm(request.getTenantId(), parallelTasks);

            // 提取各个分析结果
            String partyReview = analysisResults.get(0);
            String clauseAnalysis = analysisResults.get(1);
            String legalComplianceCheck = analysisResults.get(2);
            String internalPolicyCheck = analysisResults.get(3);
            String processComplianceCheck = analysisResults.get(4);

            // 基于前面的分析结果，评估风险
            String riskAssessment = assessContractRisks(
                request,
                clauseAnalysis,
                legalComplianceCheck,
                internalPolicyCheck
            );

            // 生成最终建议
            String reviewRecommendations = generateReviewRecommendations(
                request,
                partyReview,
                clauseAnalysis,
                legalComplianceCheck,
                internalPolicyCheck,
                riskAssessment
            );

            // 构建响应
            return buildContractReviewResponse(
                request,
                partyReview,
                clauseAnalysis,
                legalComplianceCheck,
                internalPolicyCheck,
                processComplianceCheck,
                riskAssessment,
                reviewRecommendations
            );
        } catch (Exception e) {
            log.error("Agent方式处理合同审查失败", e);
            throw new RuntimeException("合同审查处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 准备并行分析任务
     */
    private List<LlmRequest> prepareParallelAnalysisTasks(
        ContractReviewRequestDTO request,
        String partyInfo,
        String legalRegulations,
        String internalPolicies
    ) {
        List<LlmRequest> tasks = new ArrayList<>();

        // 任务1：关联方审查
        tasks.add(new LlmRequest(
            "contract_party_review",
            buildPartyReviewPrompt(request, partyInfo),
            Map.of("task", "party_review", "employeeId", request.getEmployeeId())
        ));

        // 任务2：条款分析
        tasks.add(new LlmRequest(
            "contract_clause_analysis",
            buildClauseAnalysisPrompt(request),
            Map.of("task", "clause_analysis", "employeeId", request.getEmployeeId())
        ));

        // 任务3：法律合规性检查
        tasks.add(new LlmRequest(
            "contract_legal_compliance",
            buildLegalCompliancePrompt(request, legalRegulations),
            Map.of("task", "legal_compliance", "employeeId", request.getEmployeeId())
        ));

        // 任务4：内部制度符合性检查
        tasks.add(new LlmRequest(
            "contract_policy_compliance",
            buildInternalPolicyCompliancePrompt(request, internalPolicies),
            Map.of("task", "policy_compliance", "employeeId", request.getEmployeeId())
        ));

        // 任务5：内部流程符合性检查
        tasks.add(new LlmRequest(
            "contract_process_compliance",
            buildProcessCompliancePrompt(request),
            Map.of("task", "process_compliance", "employeeId", request.getEmployeeId())
        ));

        return tasks;
    }

    /**
     * 检索关联方信息
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrievePartyInformation(ContractReviewRequestDTO request) {
        log.debug("检索合同关联方信息");

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();

        // 添加合同相关方信息（甲方、乙方）
        if (request.getPartyA() != null) {
            queryParams.put("partyA", request.getPartyA());
        }

        if (request.getPartyB() != null) {
            queryParams.put("partyB", request.getPartyB());
        }

        // 构建唯一的缓存键
        String cacheKey = "contract_party_info_" +
            (request.getPartyA() != null ? request.getPartyA().hashCode() : 0) + "_" +
            (request.getPartyB() != null ? request.getPartyB().hashCode() : 0);

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "PARTY_INFORMATION",
            cacheKey,
            queryParams
        );
    }

    /**
     * 检索相关法律法规
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveLegalRegulations(ContractReviewRequestDTO request) {
        log.debug("检索相关法律法规，合同类型: {}", request.getContractType());

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("contractType", request.getContractType());

        if (request.getIndustryType() != null) {
            queryParams.put("industryType", request.getIndustryType());
        }

        // 构建唯一的缓存键
        String cacheKey = "contract_legal_regulations_" + request.getContractType();

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "LEGAL_REFERENCE",
            cacheKey,
            queryParams
        );
    }

    /**
     * 检索相关内部制度
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveInternalPolicies(ContractReviewRequestDTO request) {
        log.debug("检索相关内部制度，企业ID: {}", request.getCompanyId());

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("companyId", request.getCompanyId());
        queryParams.put("contractType", request.getContractType());

        // 构建唯一的缓存键
        String cacheKey = "contract_internal_policies_" + request.getCompanyId() + "_" + request.getContractType();

        // 利用KnowledgeRetrievalService进行检索，自动处理缓存
        return knowledgeRetrievalService.retrieveKnowledge(
            request.getTenantId(),
            "COMPANY_POLICY",
            cacheKey,
            queryParams
        );
    }

    /**
     * 检查内部流程符合性
     */
    private String checkProcessCompliance(ContractReviewRequestDTO request) {
        log.debug("检查内部流程符合性");

        String prompt = buildProcessCompliancePrompt(request);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "contract_process_compliance",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            DEFAULT_MAX_RETRIES
        );
    }

    /**
     * 评估合同风险
     */
    private String assessContractRisks(
        ContractReviewRequestDTO request,
        String clauseAnalysis,
        String legalComplianceCheck,
        String internalPolicyCheck
    ) {
        log.debug("评估合同风险");

        String prompt = buildRiskAssessmentPrompt(request, clauseAnalysis, legalComplianceCheck, internalPolicyCheck);

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "contract_risk_assessment",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            DEFAULT_MAX_RETRIES
        );
    }

    /**
     * 生成审查建议
     */
    private String generateReviewRecommendations(
        ContractReviewRequestDTO request,
        String partyReview,
        String clauseAnalysis,
        String legalComplianceCheck,
        String internalPolicyCheck,
        String riskAssessment
    ) {
        log.debug("生成审查建议");

        String prompt = buildReviewRecommendationsPrompt(
            request, partyReview, clauseAnalysis, legalComplianceCheck, internalPolicyCheck, riskAssessment
        );

        // 使用LlmOrchestrationService进行LLM调用
        return llmOrchestrationService.invokeLlmWithRetry(
            request.getTenantId(),
            "contract_review_recommendations",
            prompt,
            Map.of("employeeId", request.getEmployeeId()),
            DEFAULT_MAX_RETRIES
        );
    }

    /**
     * 构建关联方审查提示词
     */
    private String buildPartyReviewPrompt(ContractReviewRequestDTO request, String partyInfo) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下合同关联方进行审查：\n\n");

        // 添加合同类型和双方信息
        prompt.append("【合同类型】\n").append(request.getContractType()).append("\n\n");

        if (request.getPartyA() != null) {
            prompt.append("【甲方】\n").append(request.getPartyA()).append("\n\n");
        }

        if (request.getPartyB() != null) {
            prompt.append("【乙方】\n").append(request.getPartyB()).append("\n\n");
        }

        // 添加检索到的关联方背景信息
        prompt.append("【关联方背景信息】\n").append(partyInfo).append("\n\n");

        // 添加审查要点
        prompt.append("请从以下方面进行审查：\n");
        prompt.append("1. 关联方的主体资格（是否有效存续、是否有相应资质）\n");
        prompt.append("2. 关联方的履约能力（财务状况、业务能力）\n");
        prompt.append("3. 关联方的信用状况（是否有失信记录）\n");
        prompt.append("4. 是否存在关联交易风险\n");
        prompt.append("5. 是否存在利益冲突\n");

        return prompt.toString();
    }

    /**
     * 构建条款分析提示词
     */
    private String buildClauseAnalysisPrompt(ContractReviewRequestDTO request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下合同条款进行全面分析：\n\n");
        prompt.append("【合同内容】\n").append(request.getContractContent()).append("\n\n");

        prompt.append("请从以下方面进行分析：\n");
        prompt.append("1. 合同结构完整性（是否包含必要条款）\n");
        prompt.append("2. 核心条款分析（权利义务、价格、交付、付款等）\n");
        prompt.append("3. 风险条款分析（违约责任、争议解决、终止条件等）\n");
        prompt.append("4. 条款间的一致性和冲突\n");
        prompt.append("5. 合同用语的准确性和明确性\n");

        return prompt.toString();
    }

    /**
     * 构建法律合规性检查提示词
     */
    private String buildLegalCompliancePrompt(ContractReviewRequestDTO request, String legalRegulations) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请检查以下合同的法律合规性：\n\n");
        prompt.append("【合同内容】\n").append(request.getContractContent()).append("\n\n");
        prompt.append("【相关法律法规】\n").append(legalRegulations).append("\n\n");

        prompt.append("请从以下方面进行检查：\n");
        prompt.append("1. 合同条款是否违反法律强制性规定\n");
        prompt.append("2. 是否符合行业监管要求\n");
        prompt.append("3. 是否侵犯第三方权益\n");
        prompt.append("4. 是否存在无效或可撤销条款\n");
        prompt.append("5. 是否存在其他法律风险\n");

        return prompt.toString();
    }

    /**
     * 构建内部制度符合性检查提示词
     */
    private String buildInternalPolicyCompliancePrompt(ContractReviewRequestDTO request, String internalPolicies) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请检查以下合同与企业内部制度的符合性：\n\n");
        prompt.append("【合同内容】\n").append(request.getContractContent()).append("\n\n");
        prompt.append("【内部制度】\n").append(internalPolicies).append("\n\n");

        prompt.append("请从以下方面进行检查：\n");
        prompt.append("1. 是否符合企业合同管理制度\n");
        prompt.append("2. 是否符合企业审批权限规定\n");
        prompt.append("3. 是否符合企业风险控制要求\n");
        prompt.append("4. 是否符合企业商业行为准则\n");
        prompt.append("5. 是否需要进行特殊审批\n");

        return prompt.toString();
    }

    /**
     * 构建内部流程符合性检查提示词
     */
    private String buildProcessCompliancePrompt(ContractReviewRequestDTO request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请检查以下合同与企业内部流程的符合性：\n\n");
        prompt.append("【合同类型】\n").append(request.getContractType()).append("\n\n");
        prompt.append("【合同金额】\n").append(request.getContractAmount() != null ? request.getContractAmount() : "未提供").append("\n\n");

        prompt.append("请从以下方面进行检查：\n");
        prompt.append("1. 是否需要经过特定部门审批\n");
        prompt.append("2. 是否需要法务部门审核\n");
        prompt.append("3. 是否需要财务部门审核\n");
        prompt.append("4. 是否需要高管或董事会批准\n");
        prompt.append("5. 建议的签署流程\n");

        return prompt.toString();
    }

    /**
     * 构建风险评估提示词
     */
    private String buildRiskAssessmentPrompt(
        ContractReviewRequestDTO request,
        String clauseAnalysis,
        String legalComplianceCheck,
        String internalPolicyCheck
    ) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据以下分析结果，对合同进行全面风险评估：\n\n");
        prompt.append("【条款分析】\n").append(clauseAnalysis).append("\n\n");
        prompt.append("【法律合规检查】\n").append(legalComplianceCheck).append("\n\n");
        prompt.append("【内部制度符合性】\n").append(internalPolicyCheck).append("\n\n");

        prompt.append("请提供以下内容：\n");
        prompt.append("1. 整体风险等级（高、中、低）\n");
        prompt.append("2. 风险评分（0-100分）\n");
        prompt.append("3. 主要风险点列举\n");
        prompt.append("4. 风险影响分析\n");
        prompt.append("5. 风险应对建议\n");

        return prompt.toString();
    }

    /**
     * 构建审查建议提示词
     */
    private String buildReviewRecommendationsPrompt(
        ContractReviewRequestDTO request,
        String partyReview,
        String clauseAnalysis,
        String legalComplianceCheck,
        String internalPolicyCheck,
        String riskAssessment
    ) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据以下分析结果，为合同审查提供综合建议：\n\n");
        prompt.append("【关联方审查】\n").append(partyReview).append("\n\n");
        prompt.append("【条款分析】\n").append(clauseAnalysis).append("\n\n");
        prompt.append("【法律合规检查】\n").append(legalComplianceCheck).append("\n\n");
        prompt.append("【内部制度符合性】\n").append(internalPolicyCheck).append("\n\n");
        prompt.append("【风险评估】\n").append(riskAssessment).append("\n\n");

        prompt.append("请提供以下内容：\n");
        prompt.append("1. 综合结论（建议签署、建议修改后签署、不建议签署）\n");
        prompt.append("2. 需要修改的条款和建议修改内容\n");
        prompt.append("3. 需要补充的条款\n");
        prompt.append("4. 后续流程建议\n");
        prompt.append("5. 其他注意事项\n");

        return prompt.toString();
    }

    /**
     * 构建合同审查响应
     */
    private ContractReviewResponseDTO buildContractReviewResponse(
        ContractReviewRequestDTO request,
        String partyReview,
        String clauseAnalysis,
        String legalComplianceCheck,
        String internalPolicyCheck,
        String processComplianceCheck,
        String riskAssessment,
        String reviewRecommendations
    ) {
        // 提取风险等级和评分
        RiskLevel riskLevel = extractRiskLevel(riskAssessment);
        int riskScore = extractRiskScore(riskAssessment);

        // 提取风险摘要
        String riskSummary = extractRiskSummary(riskAssessment);

        // 提取建议和后续行动
        List<String> recommendations = extractRecommendations(reviewRecommendations);
        List<String> nextActions = extractNextActions(reviewRecommendations, processComplianceCheck);

        // 构建并返回响应
        return ContractReviewResponseDTO.builder()
            .reviewId(generateReviewId())
            .overallRiskLevel(riskLevel)
            .riskScore(riskScore)
            .riskSummary(riskSummary)
            .reviewTime(Instant.now())
            .reviewDuration(calculateReviewDuration())
            .reviewStatus("COMPLETED")
            .aiModelInfo(determineAiModel(request))
            .confidence(calculateConfidence(riskAssessment))
            .recommendations(recommendations)
            .nextActions(nextActions)
            .partyAnalysis(formatPartyAnalysis(partyReview))
            .clauseIssues(formatClauseAnalysis(clauseAnalysis))
            .complianceCheck(formatLegalCompliance(legalComplianceCheck))
            .build();
    }

    /**
     * 根据风险评估提取风险等级
     */
    private RiskLevel extractRiskLevel(String riskAssessment) {
        if (riskAssessment == null) {
            return RiskLevel.MEDIUM;
        }

        String lowerCase = riskAssessment.toLowerCase();
        if (lowerCase.contains("高风险") || lowerCase.contains("重大风险") || lowerCase.contains("严重风险")) {
            return RiskLevel.HIGH;
        } else if (lowerCase.contains("低风险") || lowerCase.contains("风险较小") || lowerCase.contains("风险可控")) {
            return RiskLevel.LOW;
        } else {
            return RiskLevel.MEDIUM;
        }
    }

    /**
     * 根据风险评估提取风险评分
     */
    private int extractRiskScore(String riskAssessment) {
        // 默认风险评分
        int defaultScore = 75;

        if (riskAssessment == null) {
            return defaultScore;
        }

        try {
            // 寻找评分相关内容
            String[] lines = riskAssessment.split("\n");
            for (String line : lines) {
                if (line.contains("评分") || line.contains("分数") || line.contains("风险评分")) {
                    // 提取数字
                    String[] parts = line.replaceAll("[^0-9]", " ").trim().split("\\s+");
                    if (parts.length > 0 && !parts[0].isEmpty()) {
                        int score = Integer.parseInt(parts[0]);
                        if (score >= 0 && score <= 100) {
                            return score;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取风险评分失败，使用默认值: {}", defaultScore, e);
        }

        // 根据风险等级估算评分
        RiskLevel level = extractRiskLevel(riskAssessment);
        return switch (level) {
            case HIGH -> 90;
            case MEDIUM -> 75;
            case LOW -> 40;
        };
    }

    /**
     * 提取风险摘要
     */
    private String extractRiskSummary(String riskAssessment) {
        if (riskAssessment == null || riskAssessment.isBlank()) {
            return "无法提取风险摘要，建议进行人工审核";
        }

        // 尝试找到总结性的语句
        String[] lines = riskAssessment.split("\n");
        for (String line : lines) {
            if (line.contains("总体") || line.contains("总结") || line.contains("整体") || line.contains("综述")) {
                if (line.length() > 10 && line.length() < 100) {
                    return line.trim();
                }
            }
        }

        // 如果没有找到合适的总结语句，使用风险等级创建一个
        RiskLevel level = extractRiskLevel(riskAssessment);
        switch (level) {
            case HIGH:
                return "合同存在重大风险，建议谨慎处理，需要进行实质性修改后再考虑签署";
            case MEDIUM:
                return "合同整体风险可控，建议完善部分条款后签署";
            case LOW:
                return "合同风险较低，符合法律法规和内部制度要求，可以考虑签署";
            default:
                return "合同风险状况需进一步评估，建议由专业人员审核";
        }
    }

    /**
     * 提取建议列表
     */
    private List<String> extractRecommendations(String reviewRecommendations) {
        List<String> recommendations = new ArrayList<>();

        if (reviewRecommendations == null || reviewRecommendations.isBlank()) {
            // 默认建议
            recommendations.add("建议由法务部门进行专业审核");
            recommendations.add("完善合同重要条款的表述");
            return recommendations;
        }

        try {
            boolean inRecommendationSection = false;
            String[] lines = reviewRecommendations.split("\n");

            for (String line : lines) {
                String trimmed = line.trim();

                // 检测是否进入建议部分
                if (trimmed.contains("建议") && (trimmed.contains("修改") || trimmed.contains("条款"))) {
                    inRecommendationSection = true;
                    continue;
                }

                // 检测是否离开建议部分
                if (inRecommendationSection && (trimmed.startsWith("后续") || trimmed.startsWith("注意事项") || trimmed.isEmpty())) {
                    inRecommendationSection = false;
                }

                // 收集建议项
                if (inRecommendationSection && (trimmed.matches("^\\d+\\..*") || trimmed.startsWith("•") || trimmed.startsWith("-"))) {
                    String recommendation = trimmed.replaceFirst("^\\d+\\.\\s*|^•\\s*|^-\\s*", "").trim();
                    if (!recommendation.isEmpty() && recommendation.length() > 5) {
                        recommendations.add(recommendation);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取建议失败", e);
        }

        // 如果没有提取到建议，添加默认建议
        if (recommendations.isEmpty()) {
            recommendations.add("建议由法务部门进行专业审核");
            recommendations.add("完善合同重要条款的表述");
        }

        return recommendations;
    }

    /**
     * 提取后续行动
     */
    private List<String> extractNextActions(String reviewRecommendations, String processComplianceCheck) {
        List<String> nextActions = new ArrayList<>();

        // 首先从流程合规检查中提取
        if (processComplianceCheck != null && !processComplianceCheck.isBlank()) {
            String[] lines = processComplianceCheck.split("\n");
            for (String line : lines) {
                if (line.contains("需要") && (line.contains("审核") || line.contains("审批") || line.contains("批准"))) {
                    String action = line.trim();
                    if (action.length() > 5 && action.length() < 100) {
                        nextActions.add(action);
                    }
                }
            }
        }

        // 然后从审查建议中提取
        if (reviewRecommendations != null && !reviewRecommendations.isBlank()) {
            boolean inNextActionSection = false;
            String[] lines = reviewRecommendations.split("\n");

            for (String line : lines) {
                String trimmed = line.trim();

                // 检测是否进入后续流程部分
                if (trimmed.contains("后续") && (trimmed.contains("流程") || trimmed.contains("步骤"))) {
                    inNextActionSection = true;
                    continue;
                }

                // 检测是否离开后续流程部分
                if (inNextActionSection && (trimmed.startsWith("其他") || trimmed.isEmpty())) {
                    inNextActionSection = false;
                }

                // 收集后续流程项
                if (inNextActionSection && (trimmed.matches("^\\d+\\..*") || trimmed.startsWith("•") || trimmed.startsWith("-"))) {
                    String action = trimmed.replaceFirst("^\\d+\\.\\s*|^•\\s*|^-\\s*", "").trim();
                    if (!action.isEmpty() && action.length() > 5 && action.length() < 100) {
                        nextActions.add(action);
                    }
                }
            }
        }

        // 如果没有提取到后续行动，添加默认行动
        if (nextActions.isEmpty()) {
            nextActions.add("完善条款");
            nextActions.add("法务审核");
            nextActions.add("签署合同");
        }

        return nextActions;
    }

    /**
     * 格式化关联方分析结果
     */
    private List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> formatPartyAnalysis(String partyReview) {
        List<ContractReviewResponseDTO.PartyRiskAnalysisDTO> partyAnalysisList = new ArrayList<>();

        if (partyReview == null || partyReview.isBlank()) {
            // 创建一个默认的关联方分析对象
            ContractReviewResponseDTO.PartyRiskAnalysisDTO defaultAnalysis = new ContractReviewResponseDTO.PartyRiskAnalysisDTO();
            defaultAnalysis.setPartyName("未知关联方");
            defaultAnalysis.setRiskLevel("UNKNOWN");
            defaultAnalysis.setAnalysis("未能完成关联方分析，建议进行人工审核");
            partyAnalysisList.add(defaultAnalysis);
            return partyAnalysisList;
        }

        // 尝试从文本中提取关联方信息
        String[] paragraphs = partyReview.split("\n\n");
        for (String paragraph : paragraphs) {
            if (paragraph.contains("关联方") || paragraph.contains("合同方") || paragraph.contains("甲方") ||
                paragraph.contains("乙方") || paragraph.contains("丙方")) {

                ContractReviewResponseDTO.PartyRiskAnalysisDTO partyAnalysis = new ContractReviewResponseDTO.PartyRiskAnalysisDTO();

                // 尝试提取关联方名称
                String partyName = extractPartyName(paragraph);
                partyAnalysis.setPartyName(partyName);

                // 尝试提取风险等级
                String riskLevel = paragraph.toLowerCase().contains("高风险") ? "HIGH" :
                                   paragraph.toLowerCase().contains("低风险") ? "LOW" : "MEDIUM";
                partyAnalysis.setRiskLevel(riskLevel);

                // 设置分析内容
                partyAnalysis.setAnalysis(paragraph.trim());

                partyAnalysisList.add(partyAnalysis);
            }
        }

        // 如果没有找到任何关联方，添加一个默认项
        if (partyAnalysisList.isEmpty()) {
            ContractReviewResponseDTO.PartyRiskAnalysisDTO generalAnalysis = new ContractReviewResponseDTO.PartyRiskAnalysisDTO();
            generalAnalysis.setPartyName("合同所有方");
            generalAnalysis.setRiskLevel("MEDIUM");
            generalAnalysis.setAnalysis(partyReview.trim());
            partyAnalysisList.add(generalAnalysis);
        }

        return partyAnalysisList;
    }

    /**
     * 从文本中提取关联方名称
     */
    private String extractPartyName(String text) {
        // 尝试提取像"甲方：XXX公司"这样的模式
        for (String prefix : Arrays.asList("甲方", "乙方", "丙方", "关联方", "合同方")) {
            int index = text.indexOf(prefix + "：");
            if (index == -1) {
                index = text.indexOf(prefix + ":");
            }

            if (index != -1) {
                int start = index + prefix.length() + 1;
                int end = text.indexOf("\n", start);
                if (end == -1) {
                    end = Math.min(start + 50, text.length()); // 最多提取50个字符
                }
                return text.substring(start, end).trim();
            }
        }

        // 如果没有找到明确的模式，返回一个默认值
        return "未命名关联方";
    }

    /**
     * 生成审查ID
     */
    private Long generateReviewId() {
        return Math.abs(UUID.randomUUID().getLeastSignificantBits()) % 10000000000L;
    }

    /**
     * 计算审查耗时（模拟）
     */
    private Long calculateReviewDuration() {
        return 5000L + (long) (Math.random() * 3000);
    }

    /**
     * 确定使用的AI模型
     */
    private String determineAiModel(ContractReviewRequestDTO request) {
        if (request.getMetadata() != null && request.getMetadata().containsKey("aiModel")) {
            String specifiedModel = (String) request.getMetadata().get("aiModel");
            return LlmResponseProcessor.normalizeModelName(specifiedModel);
        }
        return "kimi"; // 默认模型
    }

    /**
     * 计算置信度
     */
    private int calculateConfidence(String riskAssessment) {
        // 默认置信度
        int defaultConfidence = 85;

        if (riskAssessment == null || riskAssessment.isBlank()) {
            return defaultConfidence;
        }

        // 根据风险评分计算置信度（风险评分高通常意味着分析更充分，置信度更高）
        int riskScore = extractRiskScore(riskAssessment);

        // 风险评分越高，置信度适度降低（反映高风险判断的谨慎性）
        // 风险评分在中间区域时，置信度较高
        if (riskScore > 80) {
            return 90 - (riskScore - 80) / 2; // 85-90之间
        } else if (riskScore > 60) {
            return 85 + (riskScore - 60) / 4; // 85-90之间
        } else {
            return 80 + (riskScore / 3); // 80-85之间
        }
    }

    /**
     * 使用重试机制执行函数
     */
    private <T> T executeWithRetry(Supplier<T> supplier, int maxRetries) throws Exception {
        Exception lastException = null;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                return supplier.get();
            } catch (Exception e) {
                lastException = e;
                log.warn("执行失败，尝试重试 ({}/{}): {}", attempt + 1, maxRetries, e.getMessage());

                // 检查是否值得重试
                if (!isRetryable(e)) {
                    log.warn("错误不可重试，放弃尝试");
                    throw e;
                }

                // 指数退避
                if (attempt < maxRetries - 1) {
                    Thread.sleep((long) Math.pow(2, attempt) * 1000);
                }
            }
        }

        throw lastException;
    }

    /**
     * 判断异常是否可以重试
     */
    private boolean isRetryable(Exception e) {
        if (e == null) return false;

        String message = e.getMessage();
        return (
            message != null &&
            (message.contains("Channel shutdown") ||
             message.contains("UNAVAILABLE") ||
             message.contains("CONNECTION_ERROR") ||
             message.contains("timeout") ||
             message.contains("请求超时"))
        );
    }

    /**
     * 格式化条款分析结果
     *
     * @param clauseAnalysis 条款分析原始文本
     * @return 格式化后的条款分析
     */
    private List<ContractReviewResponseDTO.ClauseIssueDTO> formatClauseAnalysis(String clauseAnalysis) {
        List<ContractReviewResponseDTO.ClauseIssueDTO> analysisResults = new ArrayList<>();

        if (clauseAnalysis == null || clauseAnalysis.isBlank()) {
            // 创建一个默认条款分析对象
            ContractReviewResponseDTO.ClauseIssueDTO defaultAnalysis = new ContractReviewResponseDTO.ClauseIssueDTO();
            defaultAnalysis.setClauseNumber("整体条款");
            defaultAnalysis.setClauseText("未提供条款内容");
            defaultAnalysis.setSeverity(RiskLevel.MEDIUM);
            defaultAnalysis.setDescription("未能完成条款分析，建议进行人工审核");
            analysisResults.add(defaultAnalysis);
            return analysisResults;
        }

        // 按章节或段落拆分
        String[] sections = clauseAnalysis.split("\n\n");
        for (String section : sections) {
            // 跳过空白段落
            if (section.isBlank()) {
                continue;
            }

            ContractReviewResponseDTO.ClauseIssueDTO analysis = new ContractReviewResponseDTO.ClauseIssueDTO();

            // 尝试提取条款标题
            String[] lines = section.split("\n");
            if (lines.length > 0) {
                analysis.setClauseNumber(lines[0].trim());

                // 拼接剩余内容
                StringBuilder contentBuilder = new StringBuilder();
                for (int i = 1; i < lines.length; i++) {
                    if (i > 1) {
                        contentBuilder.append("\n");
                    }
                    contentBuilder.append(lines[i].trim());
                }
                analysis.setClauseText(contentBuilder.toString());
            } else {
                analysis.setClauseNumber("未命名条款");
                analysis.setClauseText(section.trim());
            }

            // 尝试提取风险等级
            RiskLevel riskLevel = extractRiskLevelFromText(section);
            analysis.setSeverity(riskLevel);

            // 设置分析内容
            analysis.setDescription(section.trim());

            analysisResults.add(analysis);
        }

        return analysisResults;
    }

    /**
     * 格式化法律合规性检查结果
     *
     * @param legalComplianceCheck 法律合规性检查原始文本
     * @return 格式化后的法律合规性检查结果
     */
    private ContractReviewResponseDTO.ComplianceCheckResultDTO formatLegalCompliance(String legalComplianceCheck) {
        ContractReviewResponseDTO.ComplianceCheckResultDTO complianceResult = new ContractReviewResponseDTO.ComplianceCheckResultDTO();

        if (legalComplianceCheck == null || legalComplianceCheck.isBlank()) {
            // 创建一个默认合规结果
            complianceResult.setOverallCompliance(false);
            complianceResult.setComplianceScore("未知");
            complianceResult.setViolatedRegulations(Arrays.asList("未能完成法律合规性检查，建议进行人工审核"));
            complianceResult.setRecommendations(Arrays.asList("建议由法务部门进行专业审核"));
            complianceResult.setRequiredActions(Arrays.asList("人工审核"));
            return complianceResult;
        }

        // 分析合规性文本
        boolean overallCompliant = !legalComplianceCheck.toLowerCase().contains("不合规") &&
                                  !legalComplianceCheck.toLowerCase().contains("违反") &&
                                  !legalComplianceCheck.toLowerCase().contains("不符合");
        complianceResult.setOverallCompliance(overallCompliant);

        // 设置合规分数
        String complianceScore = overallCompliant ? "良好" : "需要改进";
        complianceResult.setComplianceScore(complianceScore);

        // 提取违规法规
        List<String> violatedRegulations = new ArrayList<>();
        List<String> recommendations = new ArrayList<>();
        List<String> requiredActions = new ArrayList<>();

        // 按段落拆分并分析
        String[] paragraphs = legalComplianceCheck.split("\n\n");
        for (String paragraph : paragraphs) {
            if (paragraph.isBlank()) {
                continue;
            }

            if (paragraph.toLowerCase().contains("违反") || paragraph.toLowerCase().contains("不符合")) {
                violatedRegulations.add(paragraph.trim());
            }

            if (paragraph.toLowerCase().contains("建议") || paragraph.toLowerCase().contains("应当")) {
                recommendations.add(paragraph.trim());
            }

            if (paragraph.toLowerCase().contains("需要") || paragraph.toLowerCase().contains("必须")) {
                requiredActions.add(paragraph.trim());
            }
        }

        // 如果没有提取到具体内容，使用默认值
        if (violatedRegulations.isEmpty()) {
            if (!overallCompliant) {
                violatedRegulations.add("存在合规性问题，需要进一步分析");
            }
        }

        if (recommendations.isEmpty()) {
            recommendations.add("建议由法务部门进行专业审核");
        }

        if (requiredActions.isEmpty()) {
            requiredActions.add("完善相关条款");
        }

        complianceResult.setViolatedRegulations(violatedRegulations);
        complianceResult.setRecommendations(recommendations);
        complianceResult.setRequiredActions(requiredActions);

        return complianceResult;
    }

    /**
     * 从文本中提取风险等级
     *
     * @param text 包含风险描述的文本
     * @return 提取的风险等级
     */
    private RiskLevel extractRiskLevelFromText(String text) {
        String lowerText = text.toLowerCase();

        if (lowerText.contains("高风险") || lowerText.contains("严重") ||
            lowerText.contains("重大风险") || lowerText.contains("high risk")) {
            return RiskLevel.HIGH;
        } else if (lowerText.contains("低风险") || lowerText.contains("风险较小") ||
                   lowerText.contains("无风险") || lowerText.contains("low risk")) {
            return RiskLevel.LOW;
        } else if (lowerText.contains("风险未知") || lowerText.contains("无法确定")) {
            return RiskLevel.UNKNOWN;
        } else {
            return RiskLevel.MEDIUM; // 默认为中等风险
        }
    }
}
