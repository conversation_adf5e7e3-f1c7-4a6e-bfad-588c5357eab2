/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：LlmOrchestrationService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：LLM编排服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.ModelEnsembleService;
import com.whiskerguard.ai.service.invocation.ResultIntegrationEngine;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * LLM编排服务
 * <p>
 * 负责协调和编排多个LLM调用，实现复杂的AI任务处理。
 * 提供统一的LLM调用接口和结果整合能力。
 *
 * 主要功能：
 * 1. LLM调用编排
 * 2. 多模型协同
 * 3. 结果整合
 * 4. 错误处理和重试
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class LlmOrchestrationService {

    private static final Logger log = LoggerFactory.getLogger(LlmOrchestrationService.class);

    private final AiInvocationService aiInvocationService;
    private final ResultIntegrationEngine resultIntegrationEngine;

    /**
     * 默认LLM模型列表
     */
    @Value("${application.ai.default-models:gpt-4-turbo,deepseek-ai,kimi}")
    private String defaultModels;

    /**
     * 默认超时时间（秒）
     */
    @Value("${application.ai.default-timeout-seconds:60}")
    private int defaultTimeoutSeconds = 60;

    /**
     * 默认最大重试次数
     */
    @Value("${application.ai.max-retries:3}")
    private int defaultMaxRetries = 3;

    /**
     * 线程池，用于异步任务执行
     */
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public LlmOrchestrationService(AiInvocationService aiInvocationService, ResultIntegrationEngine resultIntegrationEngine) {
        this.aiInvocationService = aiInvocationService;
        this.resultIntegrationEngine = resultIntegrationEngine;
    }

    /**
     * 单次LLM调用
     *
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @return LLM响应
     */
    public String invokeLlm(Long tenantId, String toolType, String prompt, Map<String, Object> parameters) {
        log.debug("单次LLM调用，租户: {}, 工具类型: {}", tenantId, toolType);

        try {
            Map<String, Object> mergedParameters = new HashMap<>();
            if (parameters != null) {
                mergedParameters.putAll(parameters);
            }

            // 构建AI调用请求
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO();
            aiRequest.setToolKey(toolType);
            aiRequest.setPrompt(prompt);
            aiRequest.setMetadata(mergedParameters);
            aiRequest.setTenantId(tenantId);

            // 获取当前用户ID或默认值
            Long employeeId = getEmployeeIdFromParametersOrSecurity(mergedParameters);
            aiRequest.setEmployeeId(employeeId);

            // 调用aiInvocationService
            AiRequestDTO response = aiInvocationService.invoke(aiRequest);
            return response.getResponse();
        } catch (Exception e) {
            log.error("LLM调用失败，工具类型: {}, 错误: {}", toolType, e.getMessage(), e);
            throw new RuntimeException("LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量LLM调用
     *
     * @param tenantId 租户ID
     * @param requests 请求列表
     * @return 响应列表
     */
    public List<String> batchInvokeLlm(Long tenantId, List<LlmRequest> requests) {
        log.debug("批量LLM调用，租户: {}, 请求数量: {}", tenantId, requests.size());

        try {
            return requests
                .stream()
                .map(request -> invokeLlm(tenantId, request.getToolType(), request.getPrompt(), request.getParameters()))
                .toList();
        } catch (Exception e) {
            log.error("批量LLM调用失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("批量LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量调用LLM并返回ModelResponse列表
     * 这个方法用于兼容需要ModelResponse列表的场景
     *
     * @param tenantId 租户ID
     * @param requests 请求列表
     * @return ModelResponse列表
     */
    public List<ModelEnsembleService.ModelResponse> batchInvokeModelResponses(Long tenantId, List<LlmRequest> requests) {
        List<String> stringResponses = batchInvokeLlm(tenantId, requests);
        return stringResponses.stream()
            .map(response ->
                // 使用有参构造函数而不是无参构造函数和setter方法
                new ModelEnsembleService.ModelResponse(
                    response,          // content
                    "default-model",   // modelName
                    Instant.now().toEpochMilli() // timestamp as long
                )
            )
            .collect(Collectors.toList());
    }
    /**
     * 异步LLM调用
     *
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @return 异步响应
     */
    public CompletableFuture<String> asyncInvokeLlm(Long tenantId, String toolType, String prompt, Map<String, Object> parameters) {
        log.debug("异步LLM调用，租户: {}, 工具类型: {}", tenantId, toolType);

        return CompletableFuture.supplyAsync(() -> {
            try {
                return invokeLlm(tenantId, toolType, prompt, parameters);
            } catch (Exception e) {
                log.error("异步LLM调用失败，工具类型: {}, 错误: {}", toolType, e.getMessage(), e);
                throw new RuntimeException("异步LLM调用失败: " + e.getMessage(), e);
            }
        }, executorService);
    }

    /**
     * 链式LLM调用
     * 将前一个LLM的输出作为下一个LLM的输入
     *
     * @param tenantId 租户ID
     * @param chainRequests 链式请求
     * @return 最终响应
     */
    public String chainInvokeLlm(Long tenantId, List<ChainLlmRequest> chainRequests) {
        log.debug("链式LLM调用，租户: {}, 链长度: {}", tenantId, chainRequests.size());

        try {
            String currentOutput = null;
            List<String> intermediateResults = new ArrayList<>();

            for (int i = 0; i < chainRequests.size(); i++) {
                ChainLlmRequest request = chainRequests.get(i);

                // 构建提示词，如果不是第一个请求，则包含前一个输出
                String prompt = request.getPrompt();
                if (i > 0 && currentOutput != null) {
                    // 根据上下文拼接策略组织提示词
                    prompt = buildChainPrompt(request.getPrompt(), currentOutput, request.getChainStrategy());
                }

                Map<String, Object> parameters = request.getParameters() != null ?
                    new HashMap<>(request.getParameters()) : new HashMap<>();

                // 添加链信息到参数
                parameters.put("chainStep", i + 1);
                parameters.put("chainTotal", chainRequests.size());

                // 带重试的调用LLM
                currentOutput = invokeLlmWithRetry(tenantId, request.getToolType(), prompt, parameters, defaultMaxRetries);

                // 保存中间结果
                intermediateResults.add(currentOutput);

                log.debug("链式调用第{}步完成", i + 1);
            }

            // 如果有多个结果且设置了自动整合
            if (intermediateResults.size() > 1 && chainRequests.get(0).isAutoIntegrate()) {
                log.debug("自动整合链式调用结果");
                String integrationPrompt = "请整合以下链式处理的结果，生成最终输出：\n";
                return integrateResults(tenantId, intermediateResults, integrationPrompt);
            }

            return currentOutput;
        } catch (Exception e) {
            log.error("链式LLM调用失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("链式LLM调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 并行LLM调用并整合结果
     *
     * @param tenantId 租户ID
     * @param parallelRequests 并行请求
     * @param integrationPrompt 整合提示词
     * @return 整合后的响应
     */
    public String parallelInvokeLlmWithIntegration(Long tenantId, List<LlmRequest> parallelRequests, String integrationPrompt) {
        return parallelInvokeLlmWithIntegration(tenantId, parallelRequests, integrationPrompt, defaultTimeoutSeconds);
    }

    /**
     * 并行LLM调用并整合结果（带超时控制）
     *
     * @param tenantId 租户ID
     * @param parallelRequests 并行请求
     * @param integrationPrompt 整合提示词
     * @param timeoutSeconds 超时时间（秒）
     * @return 整合后的响应
     */
    public String parallelInvokeLlmWithIntegration(Long tenantId, List<LlmRequest> parallelRequests, String integrationPrompt, int timeoutSeconds) {
        log.debug("并行LLM调用并整合，租户: {}, 并行数量: {}, 超时时间: {}秒", tenantId, parallelRequests.size(), timeoutSeconds);

        try {
            // 1. 创建并行任务
            List<CompletableFuture<ParallelTaskResult>> futures = new ArrayList<>();
            for (int i = 0; i < parallelRequests.size(); i++) {
                LlmRequest request = parallelRequests.get(i);
                int taskIndex = i;

                // 为每个请求创建一个带异常处理的Future
                CompletableFuture<ParallelTaskResult> future = asyncInvokeLlm(tenantId, request.getToolType(), request.getPrompt(), request.getParameters())
                    .thenApply(result -> new ParallelTaskResult(taskIndex, result, null))
                    .exceptionally(ex -> new ParallelTaskResult(taskIndex, null, ex));

                futures.add(future);
            }

            // 2. 等待所有任务完成或超时
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            try {
                allFutures.get(timeoutSeconds, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("部分并行任务未在规定时间内完成，将使用已完成的结果", e);
            }

            // 3. 收集所有已完成的成功结果
            List<ParallelTaskResult> results = futures.stream()
                .map(f -> {
                    try {
                        return f.getNow(null); // 不等待，立即返回当前结果
                    } catch (Exception e) {
                        log.error("获取任务结果失败", e);
                        return null;
                    }
                })
                .filter(r -> r != null && r.getResult() != null)
                .toList();

            // 如果没有任何成功结果，抛出异常
            if (results.isEmpty()) {
                throw new RuntimeException("所有并行任务均失败或超时");
            }

            // 4. 整合结果
            return integrateResults(tenantId, results.stream().map(ParallelTaskResult::getResult).toList(), integrationPrompt);
        } catch (Exception e) {
            log.error("并行LLM调用并整合失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("并行LLM调用并整合失败: " + e.getMessage(), e);
        }
    }

    /**
     * 整合多个结果
     */
    private String integrateResults(Long tenantId, List<String> results, String integrationPrompt) {
        // 使用已有的ResultIntegrationEngine服务进行整合（如果存在）或者使用默认方法
        if (resultIntegrationEngine != null) {
            // 晏海水备注
            //return resultIntegrationEngine.integrate(results, integrationPrompt);
            return resultIntegrationEngine.integrate(null, integrationPrompt);
        } else {
            // 默认整合方法
            String combinedResults = results.stream()
                .map(String::trim)
                .collect(Collectors.joining("\n\n---\n\n"));
            String finalPrompt = integrationPrompt + "\n\n【各部分结果】\n" + combinedResults;

            return invokeLlm(tenantId, "result_integration", finalPrompt, null);
        }
    }

    /**
     * 带重试的LLM调用
     *
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @param maxRetries 最大重试次数
     * @return LLM响应
     */
    public String invokeLlmWithRetry(Long tenantId, String toolType, String prompt, Map<String, Object> parameters, int maxRetries) {
        log.debug("带重试的LLM调用，租户: {}, 工具类型: {}, 最大重试次数: {}", tenantId, toolType, maxRetries);

        Exception lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return invokeLlm(tenantId, toolType, prompt, parameters);
            } catch (Exception e) {
                lastException = e;
                log.warn("LLM调用失败，尝试次数: {}/{}, 错误: {}", attempt + 1, maxRetries + 1, e.getMessage());

                if (attempt < maxRetries) {
                    // 指数退避策略，每次重试等待时间增加
                    int waitTime = (int) Math.min(1000 * Math.pow(2, attempt), 30000); // 最多等待30秒
                    log.debug("等待 {} 毫秒后重试", waitTime);
                    try {
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试过程中线程被中断", ie);
                    }
                }
            }
        }

        throw new RuntimeException("LLM调用失败，已达到最大重试次数: " + maxRetries, lastException);
    }

    /**
     * 多模型调用并选择最优结果
     *
     * @param tenantId 租户ID
     * @param toolType 工具类型
     * @param prompt 提示词
     * @param parameters 参数
     * @param selectionPrompt 选择提示词
     * @return 最优响应
     */
    public String multiModelInvokeWithSelection(Long tenantId, String toolType, String prompt, Map<String, Object> parameters, String selectionPrompt) {
        // 获取要使用的模型列表
        List<String> modelList = getModelList(parameters);
        log.debug("多模型调用，租户: {}, 工具类型: {}, 模型数量: {}", tenantId, toolType, modelList.size());

        try {
            // 创建每个模型的调用任务
            List<LlmRequest> requests = modelList.stream()
                .map(model -> {
                    Map<String, Object> modelParams = new HashMap<>();
                    if (parameters != null) {
                        modelParams.putAll(parameters);
                    }
                    modelParams.put("model", model);
                    return new LlmRequest(toolType, prompt, modelParams);
                })
                .toList();

            // 并行调用多个模型
            String finalPrompt = selectionPrompt != null ? selectionPrompt :
                "请分析以下多个模型的输出结果，选择最准确、最全面的一个作为最终回答：";

            return parallelInvokeLlmWithIntegration(tenantId, requests, finalPrompt);
        } catch (Exception e) {
            log.error("多模型调用失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("多模型调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从参数或配置获取模型列表
     */
    private List<String> getModelList(Map<String, Object> parameters) {
        // 首先检查参数中是否指定了模型
        if (parameters != null && parameters.get("models") != null) {
            Object modelsObj = parameters.get("models");
            if (modelsObj instanceof List) {
                return (List<String>) modelsObj;
            } else if (modelsObj instanceof String) {
                return Arrays.asList(((String) modelsObj).split(","));
            }
        }

        // 使用默认模型列表
        return Arrays.asList(defaultModels.split(","));
    }

    /**
     * 根据上下文拼接策略构建链式调用的提示词
     */
    private String buildChainPrompt(String prompt, String previousOutput, ChainStrategy strategy) {
        if (strategy == null) {
            strategy = ChainStrategy.APPEND; // 默认策略
        }

        switch (strategy) {
            case APPEND:
                return prompt + "\n\n【前一步输出】\n" + previousOutput;
            case PREPEND:
                return "【前一步输出】\n" + previousOutput + "\n\n" + prompt;
            case REPLACE_TAG:
                return prompt.replace("{{previous_output}}", previousOutput);
            case IGNORE:
                return prompt;
            default:
                return prompt + "\n\n【前一步输出】\n" + previousOutput;
        }
    }

    /**
     * 从参数或安全上下文获取员工ID
     */
    private Long getEmployeeIdFromParametersOrSecurity(Map<String, Object> parameters) {
        // 先从参数中获取
        if (parameters != null && parameters.get("employeeId") != null) {
            Object employeeIdObj = parameters.get("employeeId");
            if (employeeIdObj instanceof Number) {
                return ((Number) employeeIdObj).longValue();
            } else if (employeeIdObj instanceof String) {
                try {
                    return Long.parseLong((String) employeeIdObj);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }

        // 尝试从安全上下文获取用户ID
        try {
            String currentUser = SecurityUtils.getCurrentUserLogin().orElse(null);
            if (currentUser != null) {
                // 这里仅返回一个默认值，实际情况应该查询用户服务获取员工ID
                return 1L;
            }
        } catch (Exception e) {
            log.debug("从安全上下文获取用户失败", e);
        }

        // 默认返回系统用户ID
        return 0L;
    }

    /**
     * 链式上下文拼接策略
     */
    public enum ChainStrategy {
        /**
         * 将上一步输出追加到提示词后面
         */
        APPEND,

        /**
         * 将上一步输出添加到提示词前面
         */
        PREPEND,

        /**
         * 用上一步输出替换提示词中的标签
         */
        REPLACE_TAG,

        /**
         * 忽略上一步输出
         */
        IGNORE
    }

    /**
     * 并行任务结果包装类
     */
    private static class ParallelTaskResult {
        private final int index;
        private final String result;
        private final Throwable error;

        public ParallelTaskResult(int index, String result, Throwable error) {
            this.index = index;
            this.result = result;
            this.error = error;
        }

        public int getIndex() {
            return index;
        }

        public String getResult() {
            return result;
        }

        public Throwable getError() {
            return error;
        }

        public boolean isSuccess() {
            return error == null && result != null;
        }
    }

    /**
     * LLM请求对象
     */
    public static class LlmRequest {

        private String toolType;
        private String prompt;
        private Map<String, Object> parameters;

        public LlmRequest(String toolType, String prompt, Map<String, Object> parameters) {
            this.toolType = toolType;
            this.prompt = prompt;
            this.parameters = parameters;
        }

        // Getters
        public String getToolType() {
            return toolType;
        }

        public String getPrompt() {
            return prompt;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }
    }

    /**
     * 链式LLM请求对象
     */
    public static class ChainLlmRequest extends LlmRequest {
        private ChainStrategy chainStrategy = ChainStrategy.APPEND;
        private boolean autoIntegrate = false;

        public ChainLlmRequest(String toolType, String prompt, Map<String, Object> parameters) {
            super(toolType, prompt, parameters);
        }

        public ChainLlmRequest(String toolType, String prompt, Map<String, Object> parameters,
                               ChainStrategy chainStrategy, boolean autoIntegrate) {
            super(toolType, prompt, parameters);
            this.chainStrategy = chainStrategy;
            this.autoIntegrate = autoIntegrate;
        }

        public ChainStrategy getChainStrategy() {
            return chainStrategy;
        }

        public boolean isAutoIntegrate() {
            return autoIntegrate;
        }
    }
}
