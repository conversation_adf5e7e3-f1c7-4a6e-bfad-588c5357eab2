/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：KnowledgeRetrievalService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：知识检索服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.domain.KnowledgeCache;
import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;

/**
 * 知识检索服务
 * <p>
 * 负责从RAG服务检索知识，并提供缓存机制。
 * 统一管理知识检索的接口和缓存策略。
 *
 * 主要功能：
 * 1. 知识检索和缓存
 * 2. 缓存管理和过期处理
 * 3. 检索结果优化
 * 4. 多租户数据隔离
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class KnowledgeRetrievalService {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeRetrievalService.class);

    private final RetrievalServiceClient retrievalServiceClient;
    private final KnowledgeCacheRepository knowledgeCacheRepository;

    /**
     * 缓存过期时间（小时）
     */
    @Value("${application.knowledge-cache.expire-hours:24}")
    private int cacheExpireHours = 24;

    /**
     * 最小缓存文本长度
     */
    @Value("${application.knowledge-cache.min-text-length:100}")
    private int minCacheTextLength = 100;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public KnowledgeRetrievalService(RetrievalServiceClient retrievalServiceClient, KnowledgeCacheRepository knowledgeCacheRepository) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.knowledgeCacheRepository = knowledgeCacheRepository;
    }

    /**
     * 检索知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param queryParams 查询参数
     * @return 知识内容
     */
    public String retrieveKnowledge(Long tenantId, String knowledgeType, String queryKey, Map<String, Object> queryParams) {
        log.debug("检索知识内容，租户: {}, 类型: {}, 关键词: {}", tenantId, knowledgeType, queryKey);

        try {
            // 1. 尝试从缓存获取
            Optional<String> cachedContent = getCachedKnowledge(tenantId, knowledgeType, queryKey);
            if (cachedContent.isPresent()) {
                log.debug("从缓存获取知识内容成功");
                return cachedContent.get();
            }

            // 2. 从RAG服务检索
            String content = retrieveFromRagService(tenantId, knowledgeType, queryParams);

            // 3. 缓存检索结果
            if (StringUtils.hasText(content) && content.length() >= minCacheTextLength) {
                cacheKnowledge(tenantId, knowledgeType, queryKey, content);
            }

            return content;
        } catch (Exception e) {
            log.error("检索知识内容失败", e);
            throw new RuntimeException("知识检索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从缓存获取知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @return 可选的缓存内容
     */
    private Optional<String> getCachedKnowledge(Long tenantId, String knowledgeType, String queryKey) {
        try {
            // 检查是否存在有效缓存
            Optional<KnowledgeCache> cacheOpt = knowledgeCacheRepository.findByTenantIdAndTypeAndQueryKey(
                tenantId,
                knowledgeType,
                queryKey
            );

            if (cacheOpt.isPresent()) {
                KnowledgeCache cache = cacheOpt.get();
                // 检查缓存是否过期
                if (
                    cache.getCreatedAt().plusSeconds(cacheExpireHours * 3600).isAfter(Instant.now()) &&
                    StringUtils.hasText(cache.getContent())
                ) {
                    log.debug("找到有效缓存，缓存ID: {}", cache.getId());
                    return Optional.of(cache.getContent());
                } else {
                    log.debug("缓存已过期，缓存ID: {}", cache.getId());
                    // 删除过期缓存
                    knowledgeCacheRepository.delete(cache);
                }
            }
            return Optional.empty();
        } catch (Exception e) {
            log.warn("获取缓存知识失败", e);
            return Optional.empty();
        }
    }

    /**
     * 缓存知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param content 知识内容
     */
    private void cacheKnowledge(Long tenantId, String knowledgeType, String queryKey, String content) {
        try {
            // 创建新的缓存记录
            KnowledgeCache cache = new KnowledgeCache();
            cache.setTenantId(tenantId);
            cache.setType(knowledgeType);
            cache.setQueryKey(queryKey);
            cache.setContent(content);
            cache.setCreatedAt(Instant.now());
            cache.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            cache.setUpdatedAt(Instant.now());
            cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            cache.setIsDeleted(false);
            cache.setVersion(1);

            knowledgeCacheRepository.save(cache);
            log.debug("知识内容缓存成功，类型: {}, 查询关键词: {}", knowledgeType, queryKey);
        } catch (Exception e) {
            log.warn("缓存知识内容失败", e);
            // 缓存失败不应影响主要流程，只记录日志
        }
    }

    /**
     * 从RAG服务检索知识
     * 使用Spring的@Retryable实现自动重试机制
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryParams 查询参数
     * @return 检索到的知识内容
     */
    @Retryable(
        value = { RestClientException.class },
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    private String retrieveFromRagService(Long tenantId, String knowledgeType, Map<String, Object> queryParams) {
        log.debug("调用RAG服务检索知识，租户: {}, 类型: {}", tenantId, knowledgeType);

        if (queryParams == null) {
            queryParams = new HashMap<>();
        }

        // 确保添加租户ID参数
        if (tenantId != null) {
            queryParams.put("tenantId", tenantId);
        }

        // 从查询参数中提取查询文本，如果不存在则使用空字符串
        String query = String.valueOf(queryParams.getOrDefault("query", ""));

        // 根据知识类型调用不同的检索接口
        try {
            // 从查询参数中提取相关字段
            String regulationId = query.get("regulationId") != null ? query.get("regulationId").toString() : null;
            String industryType = query.get("industryType") != null ? query.get("industryType").toString() : null;
            String contractType = query.get("contractType") != null ? query.get("contractType").toString() : null;
            String policyType = query.get("policyType") != null ? query.get("policyType").toString() : null;
            Long companyId = query.get("companyId") != null ? Long.valueOf(query.get("companyId").toString()) : null;

            switch (knowledgeType) {
                case "REGULATION":
                    return retrievalServiceClient.retrieveRegulations(tenantId, regulationId, industryType);
                case "INDUSTRY_PRACTICE":
                    return retrievalServiceClient.retrieveBestPractices(tenantId, industryType, policyType);
                case "COMPANY_POLICY":
                    return retrievalServiceClient.retrievePolicies(tenantId, companyId, policyType);
                case "LEGAL_REFERENCE":
                    return retrievalServiceClient.retrieveRegulations(tenantId, regulationId, industryType);
                case "CONTRACT_TEMPLATE":
                    return retrievalServiceClient.retrieveContracts(tenantId, null, contractType);
                default:
                    // 通用检索接口
                    return retrievalServiceClient.retrieveRegulations(tenantId, regulationId, industryType);
            }
        } catch (Exception e) {
            log.error("RAG服务调用失败，类型: {}", knowledgeType, e);
            throw new RuntimeException("RAG服务调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理指定类型的过期缓存
     *
     * @param knowledgeType 知识类型
     * @return 清理的缓存数量
     */
    @Transactional
    public int cleanExpiredCache(String knowledgeType) {
        Instant expirationTime = Instant.now().minusSeconds(cacheExpireHours * 3600);
        int count = knowledgeCacheRepository.deleteByTypeAndCreatedAtBefore(knowledgeType, expirationTime);
        log.info("清理过期缓存完成，类型: {}, 数量: {}", knowledgeType, count);
        return count;
    }

    /**
     * 清理所有过期缓存
     *
     * @return 清理的缓存数量
     */
    @Transactional
    public int cleanAllExpiredCache() {
        Instant expirationTime = Instant.now().minusSeconds(cacheExpireHours * 3600);
        int count = knowledgeCacheRepository.deleteByCreatedAtBefore(expirationTime);
        log.info("清理所有过期缓存完成，数量: {}", count);
        return count;
    }

    /**
     * 更新知识缓存
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param content 新的知识内容
     * @return 更新是否成功
     */
    @Transactional
    public boolean updateKnowledgeCache(Long tenantId, String knowledgeType, String queryKey, String content) {
        try {
            Optional<KnowledgeCache> cacheOpt = knowledgeCacheRepository.findByTenantIdAndTypeAndQueryKey(
                tenantId,
                knowledgeType,
                queryKey
            );

            if (cacheOpt.isPresent()) {
                KnowledgeCache cache = cacheOpt.get();
                cache.setContent(content);
                cache.setUpdatedAt(Instant.now());
                cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                cache.setVersion(cache.getVersion() + 1);
                knowledgeCacheRepository.save(cache);
                log.debug("更新知识缓存成功，缓存ID: {}", cache.getId());
                return true;
            } else {
                // 如果不存在，则创建新缓存
                cacheKnowledge(tenantId, knowledgeType, queryKey, content);
                return true;
            }
        } catch (Exception e) {
            log.error("更新知识缓存失败", e);
            return false;
        }
    }
}
