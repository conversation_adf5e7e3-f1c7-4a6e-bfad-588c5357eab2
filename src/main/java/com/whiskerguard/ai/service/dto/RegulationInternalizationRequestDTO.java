package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

@Schema(description = "外规内化请求DTO")
public class RegulationInternalizationRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "租户ID不能为空")
    @Schema(description = "租户ID", required = true, example = "1")
    private Long tenantId;

    @Schema(description = "法规ID", example = "REG001")
    private String regulationId;

    @Schema(description = "法规名称")
    private String regulationName;

    @NotBlank(message = "法规内容不能为空")
    @Size(max = 10000, message = "法规内容长度不能超过10000个字符")
    @Schema(description = "法规内容", required = true)
    private String regulationContent;

    @NotBlank(message = "行业类型不能为空")
    @Schema(description = "行业类型", required = true, example = "电力")
    private String industryType;

    @Schema(description = "行业编码")
    private String industryCode;

    @Schema(description = "公司ID", example = "1")
    private Long companyId;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司规模", example = "大型")
    private String companyScale;

    @Schema(description = "特殊要求")
    private String specialRequirements;

    @Schema(description = "员工ID")
    private Long employeeId;

    @Schema(description = "制度类型")
    private String policyType;

    @Schema(description = "模型名称列表")
    private String[] modelNames;

    @Schema(description = "公司属性")
    private Map<String, Object> companyAttributes;

    public RegulationInternalizationRequestDTO() {}

    // Getter和Setter方法
    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getRegulationId() {
        return regulationId;
    }

    public void setRegulationId(String regulationId) {
        this.regulationId = regulationId;
    }

    public String getRegulationContent() {
        return regulationContent;
    }

    public void setRegulationContent(String regulationContent) {
        this.regulationContent = regulationContent;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyScale() {
        return companyScale;
    }

    public void setCompanyScale(String companyScale) {
        this.companyScale = companyScale;
    }

    public String getSpecialRequirements() {
        return specialRequirements;
    }

    public void setSpecialRequirements(String specialRequirements) {
        this.specialRequirements = specialRequirements;
    }

    public String getRegulationName() {
        return regulationName;
    }

    public void setRegulationName(String regulationName) {
        this.regulationName = regulationName;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String[] getModelNames() {
        return modelNames;
    }

    public void setModelNames(String[] modelNames) {
        this.modelNames = modelNames;
    }

    public Map<String, Object> getCompanyAttributes() {
        return companyAttributes;
    }

    public void setCompanyAttributes(Map<String, Object> companyAttributes) {
        this.companyAttributes = companyAttributes;
    }
}
