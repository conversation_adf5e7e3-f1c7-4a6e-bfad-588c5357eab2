/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：CompanyInfoDTO.java
 * 包    名：com.whiskerguard.ai.client.dto
 * 描    述：企业信息综合DTO
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 企业信息综合DTO
 * <p>
 * 用于整合企业的基本信息、风险信息、信用信息等，
 * 为内部制度审查提供企业背景数据支持。
 *
 * 该DTO整合了多个专门的企业信息DTO的核心字段，
 * 提供统一的企业信息访问接口。
 */
public class CompanyInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 企业ID */
    private Long id;

    /** 企业名称 */
    private String name;

    /** 企业名称 */
    private String companyName;

    /** 统一社会信用代码 */
    private String creditCode;

    /** 法定代表人 */
    private String legalRepresentative;

    /** 注册资本 */
    private String registeredCapital;

    /** 成立日期 */
    private String establishmentDate;

    /** 经营状态 */
    private String businessStatus;

    /** 企业类型 */
    private String companyType;

    /** 所属行业 */
    private String industry;

    /** 注册地址 */
    private String registeredAddress;

    /** 经营范围 */
    private String businessScope;

    /** 企业规模 */
    private String companyScale;

    /** 员工人数 */
    private Integer employeeCount;

    /** 信用评级 */
    private String creditRating;

    /** 信用分数 */
    private Integer creditScore;

    /** 风险等级 */
    private String riskLevel;

    /** 风险分数 */
    private Integer riskScore;

    /** 经营异常记录数量 */
    private Integer abnormalCount;

    /** 行政处罚记录数量 */
    private Integer penaltyCount;

    /** 诉讼记录数量 */
    private Integer lawsuitCount;

    /** 失信记录数量 */
    private Integer dishonestyCount;

    /** 联系电话 */
    private String contactPhone;

    /** 联系邮箱 */
    private String contactEmail;

    /** 官方网站 */
    private String website;

    /** 数据更新时间 */
    private LocalDate dataUpdateTime;

    /** 数据来源 */
    private String dataSource;

    /** 附加信息 */
    private Map<String, Object> additionalInfo = new HashMap<>();

    public CompanyInfoDTO() {}

    public CompanyInfoDTO(String companyName, String creditCode, String legalRepresentative) {
        this.companyName = companyName;
        this.creditCode = creditCode;
        this.legalRepresentative = legalRepresentative;
    }

    /**
     * 从基本信息DTO创建
     */
    public static CompanyInfoDTO fromBasicInfo(CompanyBasicInfoDTO basicInfo) {
        CompanyInfoDTO dto = new CompanyInfoDTO();
        dto.setCompanyName(basicInfo.getCompanyName());
        dto.setCreditCode(basicInfo.getCreditCode());
        dto.setLegalRepresentative(basicInfo.getLegalRepresentative());
        dto.setRegisteredCapital(basicInfo.getRegisteredCapital());
        dto.setEstablishmentDate(basicInfo.getEstablishDate() != null ? basicInfo.getEstablishDate().toString() : null);
        dto.setBusinessStatus(basicInfo.getStatus());
        dto.setCompanyType(basicInfo.getCompanyType());
        dto.setIndustry(basicInfo.getIndustry());
        dto.setRegisteredAddress(basicInfo.getRegisteredAddress());
        dto.setBusinessScope(basicInfo.getBusinessScope());
        dto.setCompanyScale(basicInfo.getCompanyScale());
        dto.setEmployeeCount(basicInfo.getEmployeeCount());
        dto.setContactPhone(basicInfo.getContactPhone());
        dto.setContactEmail(basicInfo.getContactEmail());
        dto.setWebsite(basicInfo.getWebsite());
        dto.setDataUpdateTime(basicInfo.getDataUpdateTime());
        dto.setDataSource("天眼查基本信息");
        return dto;
    }

    /**
     * 合并风险信息
     */
    public void mergeRiskInfo(CompanyRiskInfoDTO riskInfo) {
        if (riskInfo != null) {
            this.riskLevel = riskInfo.getRiskLevel();
            this.riskScore = riskInfo.getRiskScore();
            this.abnormalCount = riskInfo.getAbnormalCount();
            this.penaltyCount = riskInfo.getPenaltyCount();
        }
    }

    /**
     * 合并信用信息
     */
    public void mergeCreditInfo(CompanyCreditInfoDTO creditInfo) {
        if (creditInfo != null) {
            this.creditRating = creditInfo.getCreditLevel();
            this.creditScore = creditInfo.getCreditScore();
            this.dishonestyCount = creditInfo.getDishonestyCount();
        }
    }

    /**
     * 合并诉讼信息
     */
    public void mergeLawsuitInfo(CompanyLawsuitInfoDTO lawsuitInfo) {
        if (lawsuitInfo != null) {
            this.lawsuitCount = lawsuitInfo.getLawsuitCount();
            if (this.dishonestyCount == null) {
                this.dishonestyCount = lawsuitInfo.getDishonestyCount();
            }
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public String getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(String registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getEstablishmentDate() {
        return establishmentDate;
    }

    public void setEstablishmentDate(String establishmentDate) {
        this.establishmentDate = establishmentDate;
    }

    public String getBusinessStatus() {
        return businessStatus;
    }

    public void setBusinessStatus(String businessStatus) {
        this.businessStatus = businessStatus;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getCompanyScale() {
        return companyScale;
    }

    public void setCompanyScale(String companyScale) {
        this.companyScale = companyScale;
    }

    public Integer getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Integer employeeCount) {
        this.employeeCount = employeeCount;
    }

    public String getCreditRating() {
        return creditRating;
    }

    public void setCreditRating(String creditRating) {
        this.creditRating = creditRating;
    }

    public Integer getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(Integer creditScore) {
        this.creditScore = creditScore;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public void setRiskScore(Integer riskScore) {
        this.riskScore = riskScore;
    }

    public Integer getAbnormalCount() {
        return abnormalCount;
    }

    public void setAbnormalCount(Integer abnormalCount) {
        this.abnormalCount = abnormalCount;
    }

    public Integer getPenaltyCount() {
        return penaltyCount;
    }

    public void setPenaltyCount(Integer penaltyCount) {
        this.penaltyCount = penaltyCount;
    }

    public Integer getLawsuitCount() {
        return lawsuitCount;
    }

    public void setLawsuitCount(Integer lawsuitCount) {
        this.lawsuitCount = lawsuitCount;
    }

    public Integer getDishonestyCount() {
        return dishonestyCount;
    }

    public void setDishonestyCount(Integer dishonestyCount) {
        this.dishonestyCount = dishonestyCount;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public LocalDate getDataUpdateTime() {
        return dataUpdateTime;
    }

    public void setDataUpdateTime(LocalDate dataUpdateTime) {
        this.dataUpdateTime = dataUpdateTime;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Map<String, Object> getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(Map<String, Object> additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyInfoDTO that = (CompanyInfoDTO) o;
        return Objects.equals(companyName, that.companyName) && Objects.equals(creditCode, that.creditCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyName, creditCode);
    }

    @Override
    public String toString() {
        return (
            "CompanyInfoDTO{" +
            "companyName='" +
            companyName +
            '\'' +
            ", creditCode='" +
            creditCode +
            '\'' +
            ", legalRepresentative='" +
            legalRepresentative +
            '\'' +
            ", businessStatus='" +
            businessStatus +
            '\'' +
            ", creditRating='" +
            creditRating +
            '\'' +
            ", riskLevel='" +
            riskLevel +
            '\'' +
            '}'
        );
    }
}
